```mermaid
flowchart TD
    A[开始处理] --> B[添加旋转角度]
    B --> C{是否为离线OCR用户<br/>且GPU服务器可用?}
    
    C -->|是| D{判断客户端类型<br/>是否为API发送}
    C -->|否| M[获取任务的ocrResult]
    
    D -->|是 API发送| E[请求API识别_全流程]
    D -->|否 非API发送| J[请求API识别_OCR]
    
    E --> F[记录API
    识别结果]
    F --> G{结果中是否有<br/>qrContent?}
    
    G -->|有| H[使用JahisParser解析QR内容]
    H --> I{是否成功生成<br/>处方?}
    
    I -->|是| K[设置处方属性<br/>endFlag=END<br/>qrFlag=YES<br/>isGPU=1]
    K --> L[返回QR处方结果]
    
    I -->|否| N[处理离线处方]
    G -->|无| N
    
    J --> O[记录OCR识别结果]
    O --> P{OCR结果是否<br/>为空?}
    
    P -->|不为空| N
    P -->|为空| Q[处理普通处方识别]
    
    M --> R{ocrResult是否<br/>为空?}
    R -->|不为空| N
    R -->|为空| Q
    
    N --> S[离线处方处理流程]
    Q --> T[普通处方处理流程]
    
    S --> U[返回离线处方结果]
    T --> V[返回普通处方结果]
    
    style A fill:#e1f5fe
    style L fill:#c8e6c9
    style U fill:#c8e6c9
    style V fill:#c8e6c9
    style C fill:#fff3e0
    style D fill:#fff3e0
    style G fill:#fff3e0
    style I fill:#fff3e0
    style P fill:#fff3e0
    style R fill:#fff3e0
```
