```mermaid
flowchart TD
    subgraph "图片预处理阶段"
        A([输入图片]) --> B[加载图片]
        B --> C{分类模型过滤?}
        C -->|是| D[过滤无关图片]
        C -->|否| G[继续处理]
        D --> E{是否需要旋转?}
        E -->|是| F[旋转图片]
        E -->|否| G
        F --> G
    end

    subgraph "AI识别处理阶段"
        G --> H[分割模型处理]
        H --> I[结构检测模型处理]
        I --> J{是否识别QR码?}
        J -->|是| K[解码QR码]
        J -->|否| L[处方结构处理<br/>头部/处方/尾部]
        K --> L
    end

    subgraph "结果处理阶段"
        L --> M[OCR识别]
        M --> N[后处理与结果合并]
        N --> O([输出结果])
    end

    %% 样式类定义
    classDef startEnd fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef process fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef aiProcess fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef output fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32

    %% 应用样式类
    class A,O startEnd
    class B,D,F,G,K,L,M,N process
    class C,E,J decision
    class H,I aiProcess
```
